package com.nexla.telemetry.jmx;

import static com.codahale.metrics.MetricRegistry.name;

import com.codahale.metrics.*;
import com.codahale.metrics.jmx.JmxReporter;
import java.util.function.Supplier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SimpleJmxMetrics {
  private static final Logger LOGGER = LoggerFactory.getLogger(SimpleJmxMetrics.class);
  private static final MetricRegistry METRIC_REGISTRY;

  static {
    METRIC_REGISTRY = new MetricRegistry();
    startJmxReporter();
  }

  public static Meter meter(Class<?> clazz, String... names) {
    return METRIC_REGISTRY.meter(name(name(clazz, names), "meter"));
  }

  public static Timer timer(Class<?> clazz, String... names) {
    return METRIC_REGISTRY.timer(name(name(clazz, names), "timer"));
  }

  public static <T> Gauge<T> gauge(Supplier<T> gauge, Class<?> clazz, String... names) {
    return METRIC_REGISTRY.gauge(name(name(clazz, names), "gauge"), () -> gauge::get);
  }

  public static Histogram histogram(Class<?> clazz, String... names) {
    return METRIC_REGISTRY.histogram(name(name(clazz, names), "histogram"));
  }

  private static void startJmxReporter() {
    try {
      JmxReporter.forRegistry(METRIC_REGISTRY).inDomain("nexla.metrics").build().start();
    } catch (Exception e) {
      LOGGER.error("Failed to start JMX reporter", e);
    }
  }
}
