package com.nexla.common.listing;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum ListingUpdateTaskType {
  LISTING_UPDATE_EXTERNAL,
  LISTING_UPDATE_HEARTBEAT,
  LISTING_UPDATE_FILE_REINGEST_BATCH,
  LISTING_UPDATE_FILE_REINGEST,
  LISTING_UPDATE_SET_FILE_STATUS,
  LISTING_UPDATE_SET_TMP_LISTING_FILE_STATUS,
  LISTING_UPDATE_POST_TMP_LISTING_FILE,
  LISTING_UPDATE_SET_FILE_STATUS_PATH_MASK,
  LISTING_UPDATE_POST_ADAPTIVE_FLOW_TASK,
  LISTING_UPDATE_SET_ADAPTIVE_FLOW_TASK_STATUS;

  @SuppressWarnings("unused")
  @JsonCreator
  public static ListingUpdateTaskType fromString(String string) {
    return ListingUpdateTaskType.valueOf(string.toUpperCase());
  }
}
