package connect.jdbc.sink.dialect.copy;

import com.databricks.sdk.WorkspaceClient;
import com.databricks.sdk.core.DatabricksConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DatabricksDialect;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Implementation of SinkCopyOperation for Databricks Unity Volume.
 * Assumes 'config' and 'schema' are initialized by the superclass.
 * This class handles:
 * 1. Uploading a local file to a Databricks Unity Catalog Volume using the Databricks SDK.
 * 2. Loading data from the Unity Volume into a Databricks Delta table using COPY INTO.
 * It also supports table creation, MERGE operations, and Volume creation.
 */
public class SinkDatabricksUnityVolumeCopyOperation extends BaseSinkCopyOperation {

    private static final Logger logger = LoggerFactory.getLogger(SinkDatabricksUnityVolumeCopyOperation.class);
    private static final DatabricksDialect DIALECT = new DatabricksDialect();

    private WorkspaceClient databricksClient;
    // 'this.config' and 'this.schema' are assumed to be inherited and initialized by BaseSinkCopyOperation.

    @Override
    @SneakyThrows
    protected void executeCopyCommand(String stagingFileCloudPath, Connection connection, Optional<ReplicationContext> replicationContext) {
        createVolumeIfNotExists(connection); // Ensure volume exists

        final boolean tableExist = isTableExist(connection, this.config.table);
        if (tableExist) {
            executeStatement(connection, copyCommand(this.schema, stagingFileCloudPath, this.config.table, replicationContext));
        } else {
            logger.info("Table {} does not exist. Creating it from data at Volume path: {}", getQualifiedTableName(), stagingFileCloudPath);
            executeStatement(connection, createDeltaTableCommand(stagingFileCloudPath));
        }
    }

    @Override
    @SneakyThrows
    protected void executeUpsert(Statement st, File localFile, String tempTableUnused, Optional<ReplicationContext> replicationContext) {
        Connection conn = st.getConnection();
        createVolumeIfNotExists(conn); // Ensure volume exists

        // uploadFile(localFile) should have been called by the base class's process flow
        // before this method if this is an upsert operation needing the file in the cloud.
        // The 'localFile' parameter is used to derive the 'volumePath'.
        String volumePath = tempFileLocation(localFile.getName());

        final boolean tableExist = isTableExist(conn, this.config.table);
        logger.info("Delta table {} exists: {}. Proceeding with MERGE.", getQualifiedTableName(), tableExist);
        if (tableExist) {
            logUpdated(st.executeUpdate(logSql(mergeCommand(this.config.table, this.config.primaryKey, volumePath))));
        } else {
            logger.info("Table {} does not exist. Creating it from data at Volume path: {} before attempting MERGE.", getQualifiedTableName(), volumePath);
            executeStatement(conn, createDeltaTableCommand(volumePath));
            logger.info("Table {} created. For this batch, data is loaded. Subsequent batches can MERGE.", getQualifiedTableName());
        }
    }

    public boolean isTableExist(Connection connection, String tableNameInConfig) throws SQLException {
        String catalog = this.config.authConfig.databaseName;
        String schema = this.config.authConfig.schemaName;
        String qualifiedTableNameToCheck = String.format("%s.%s.%s", catalog, schema, tableNameInConfig);

        String checkTableSql = String.format("SELECT table_name FROM %s.%s.INFORMATION_SCHEMA.TABLES WHERE table_catalog = '%s' AND table_schema = '%s' AND table_name = '%s'",
                DIALECT.q(catalog), DIALECT.q(schema),
                catalog, schema, tableNameInConfig);

        logger.info("Checking existence of table using SQL: {}", checkTableSql);

        try (Statement checkStatement = connection.createStatement();
             ResultSet rs = checkStatement.executeQuery(checkTableSql)) {
            if (rs.next()) {
                logger.info("Table {} found.", qualifiedTableNameToCheck);
                return true;
            }
        } catch (SQLException e) {
            logger.warn("SQL error while checking table existence for {}: {}. Assuming table does not exist.", qualifiedTableNameToCheck, e.getMessage());
            return false;
        }
        logger.info("Table {} not found.", qualifiedTableNameToCheck);
        return false;
    }


    @Override
    public DbDialect dbDialect() {
        return DIALECT;
    }

    @Override
    @SneakyThrows
    protected void runUpsert(Optional<List<String>> columnsOpt, File localFile, Connection conn, Optional<ReplicationContext> replicationContext) {
        // BaseSinkCopyOperation.processRecords should handle calling uploadFile(localFile)
        // before this if this is part of that flow.
        // To be safe, or if the flow requires it here:
        logger.info("runUpsert: Ensuring local file {} is uploaded before executing MERGE.", localFile.getName());
        uploadFile(localFile); // Ensures file is on the Volume

        try (Statement st = conn.createStatement()) {
            executeUpsert(st, localFile, StringUtils.EMPTY, replicationContext);
        }
    }

    private String mergeCommand(String tableNameUnqualified, List<String> primaryKeys, String sourceVolumePath) {
        if (primaryKeys == null || primaryKeys.isEmpty()) {
            throw new IllegalArgumentException("Primary key(s) must be defined for MERGE operation. Please check 'primary.key' configuration.");
        }
        final String condition = primaryKeys.stream()
                .map(k -> String.format("target.%s = source.%s", DIALECT.q(k), DIALECT.q(k)))
                .collect(Collectors.joining(" AND "));

        return String.format("MERGE INTO %s target\n" +
                        "USING json.`%s` source\n" +
                        "ON %s\n" +
                        "WHEN MATCHED THEN\n" +
                        "  UPDATE SET *\n" +
                        "WHEN NOT MATCHED\n" +
                        "  THEN INSERT *",
                getQualifiedTableName(),
                sourceVolumePath, condition);
    }

    @Override
    protected void initCloudObjectStoreClient() {
        // Extract workspace URL from JDBC URL and PAT token from password
        String jdbcUrl = this.config.authConfig.url;
        String patToken = this.config.authConfig.password;

        if (StringUtils.isBlank(jdbcUrl) || StringUtils.isBlank(patToken)) {
            String msg = "Databricks JDBC URL or PAT token is not configured. Cannot initialize WorkspaceClient.";
            logger.error(msg);
            throw new IllegalStateException(msg);
        }

        // Extract workspace URL from JDBC URL
        // JDBC URL format: ***************************************************************;...
        String workspaceUrl = extractWorkspaceUrlFromJdbcUrl(jdbcUrl);

        DatabricksConfig databricksConfig = new DatabricksConfig()
                .setHost(workspaceUrl)
                .setToken(patToken);
        this.databricksClient = new WorkspaceClient(databricksConfig);
        logger.info("Databricks WorkspaceClient initialized for host: {}", workspaceUrl);
    }

    private String extractWorkspaceUrlFromJdbcUrl(String jdbcUrl) {
        // Extract hostname from JDBC URL
        // Format: *******************************/...
        try {
            String urlPart = jdbcUrl.substring("jdbc:databricks://".length());
            String hostname = urlPart.split("/")[0].split(":")[0];
            return "https://" + hostname;
        } catch (Exception e) {
            String msg = "Failed to extract workspace URL from JDBC URL: " + jdbcUrl;
            logger.error(msg, e);
            throw new IllegalStateException(msg, e);
        }
    }

    @Override
    protected void deleteCloudFile(File localFile) {
        // Note: Using tempS3Delete as a generic "temp file delete" flag for Unity Volume
        // This follows the pattern where tempS3Delete is used for S3 and tempAzureBlobDelete for Azure
        if (this.config.tempS3Delete && this.databricksClient != null) {
            String volumePath = tempFileLocation(localFile.getName());
            try {
                logger.info("***** Attempting to delete file from Unity Volume: {} *****", volumePath);
                databricksClient.files().delete(volumePath);
                logger.info("***** Successfully deleted file from Unity Volume: {} *****", volumePath);
            } catch (Exception e) {
                logger.error("***** Failed to delete file from Unity Volume: {}. Error: {} *****", volumePath, e.getMessage(), e);
            }
        } else {
            logger.info("***** Skipping deletion of Unity Volume file: {} (delete flag: {}, client initialized: {}) *****",
                    localFile.getName(), this.config.tempS3Delete, this.databricksClient != null);
        }
    }

    @Override
    @SneakyThrows
    public void uploadFile(File localFile) {
        if (this.databricksClient == null) {
            logger.warn("Databricks client was null in uploadFile. Attempting to initialize (this should ideally be done by base class lifecycle).");
            initCloudObjectStoreClient(); // Attempt to initialize if not done
            if (this.databricksClient == null) { // Still null
                String msg = "Databricks client not initialized after attempt in uploadFile. Cannot upload file to Unity Volume.";
                logger.error(msg);
                throw new IllegalStateException(msg);
            }
        }

        String volumePath = tempFileLocation(localFile.getName());
        logger.info("Unity Volume: Uploading local file {} to Databricks Volume path: {}",
                localFile.getAbsolutePath(), volumePath);

        try (InputStream inputStream = new FileInputStream(localFile)) {
            databricksClient.files().upload(volumePath, inputStream);
            logger.info("File uploaded successfully from local {} to Unity Volume: {}", localFile.getAbsolutePath(), volumePath);
        } catch (Exception e) {
            logger.error("Failed to upload file {} to Unity Volume path {}: {}",
                    localFile.getAbsolutePath(), volumePath, e.getMessage(), e);
            throw new RuntimeException("File upload to Unity Volume failed for " + localFile.getName() +
                    " to path " + volumePath, e);
        }
    }

    @Override
    protected String getTempFilePrefix() {
        return "local_dbx_unity_staging_";
    }

    @Override
    String tempFileLocation(String localFileName) {
        String catalog = this.config.authConfig.databaseName;
        String schema = this.config.authConfig.schemaName;
        String volumeName = this.config.table; // Using table name as volume name

        if (StringUtils.isAnyBlank(catalog, schema, volumeName)) {
            String msg = "Databricks catalog, schema, or volume name (derived from table) is not configured for Unity Volume path construction.";
            logger.error(msg);
            throw new IllegalStateException(msg);
        }

        String cloudFileName = new File(localFileName).getName();
        if (!cloudFileName.toLowerCase().endsWith(".json") && !cloudFileName.toLowerCase().endsWith(".json.gz")) {
            cloudFileName += ".json";
        }

        String volumePath = String.format("/Volumes/%s/%s/%s/%s",
                catalog,
                schema,
                volumeName,
                cloudFileName);
        logger.debug("Constructed Unity Volume path: {}", volumePath);
        return volumePath;
    }

    @Override
    public String copyCommand(Schema schema, String volumeLocation, String tableNameUnqualified, Optional<ReplicationContext> replicationContext) {
        return String.format("COPY INTO %s\n" +
                        "FROM '%s'\n" +
                        "FILEFORMAT = JSON\n" +
                        "COPY_OPTIONS ('force' = 'false', 'mergeSchema' = 'true')",
                getQualifiedTableName(), volumeLocation);
    }

    private String createDeltaTableCommand(String volumeLocation) {
        return String.format("CREATE TABLE IF NOT EXISTS %s\n" +
                        "USING delta\n" +
                        "AS SELECT *\n" +
                        "FROM json.`%s`",
                getQualifiedTableName(), volumeLocation);
    }

    @Override
    protected String createCommand(String tempTableUnused, String qualifiedTableNameUnused) {
        return ""; // Not used
    }

    @Override
    public void dropTempTable(Statement stmt, String tempTableUnused) {
        // Not used
    }

    private String getQualifiedTableName() {
        String catalog = this.config.authConfig.databaseName;
        String schema = this.config.authConfig.schemaName;
        String table = this.config.table;

        String qualifiedName = String.format("%s.%s.%s",
                DIALECT.q(catalog),
                DIALECT.q(schema),
                DIALECT.q(table));
        logger.debug("Unity Catalog qualified table name: {}", qualifiedName);
        return qualifiedName;
    }

    @SneakyThrows
    private void executeStatement(Connection connection, String sql) {
        logger.info("Executing SQL on Databricks: {}", sql);
        try (PreparedStatement statement = connection.prepareStatement(logSql(sql))) { // logSql from base
            statement.execute();
            logger.info("SQL executed successfully.");
        } catch (SQLException e) {
            logger.error("Error executing SQL on Databricks: {}. SQL: {}", e.getMessage(), sql, e);
            throw e;
        }
    }

    @SneakyThrows
    private void createVolumeIfNotExists(Connection connection) {
        String catalog = this.config.authConfig.databaseName;
        String schema = this.config.authConfig.schemaName;
        String volumeName = this.config.table; // Using table name as volume name

        logger.info("Ensuring Unity Volume exists - Catalog: {}, Schema: {}, Volume: {}",
                catalog, schema, volumeName);

        String createVolumeSql = String.format("CREATE VOLUME IF NOT EXISTS %s.%s.%s",
                DIALECT.q(catalog), DIALECT.q(schema), DIALECT.q(volumeName));
        executeStatement(connection, createVolumeSql);
        logger.info("Volume '{}.{}.{}' is ready or already existed.", catalog, schema, volumeName);
    }

    @Override
    public void close(Supplier<Connection> connectionProvider) {
        logger.info("SinkDatabricksUnityVolumeCopyOperation closed. Databricks WorkspaceClient does not require explicit closing.");
        this.databricksClient = null;
    }

    // ReplicationContext class definition if it's specific and not globally available
    // public static class ReplicationContext {} // Placeholder if needed
}