# Editors
.idea/
*.iml
*.iws
.vscode/*

# vs code
.vscode/

# Mac
.DS_Store

# Maven
log/
target/
*/**/target/
dependency-reduced-pom.xml
pom.xml.versionsBackup

# gradle
.gradle
build/

# misc
*.log

!otel-drop-trace-extension/README.md

831.x12.fromxml
831.x12.xml
AdminApi.postman_collection.json
JPMCROOTCA.cer
README.md
pom.xml
import-rds-certs.sh
generate_certificates.txt
Jenkinsfile
customer_root_ca.cer
lombok.config
data_new.xml
release_notes.md

email-connector-app/
iceberg-probe/
kafka-connect-mongodb-sink/
metrics-cron/
search-service/
error-monitor/
kafka-connect-redis-sink/
metrics-http/
sink-agent/
control-consumer/
fast-connector/
ingestion-service/
kafka-connect-rest-sink/
mongodb-service/
ctrl-cron/
fast-connector-common/
inmemory-connector/
kafka-connect-rest-source/
monitoring-service/
sql-probe-service/
ctrl-eventgenerator/
file-service/
integration-testing/
kafka-connect-soap-sink/
nexla-spark-agent/
statistics-app/
ctrl-http/
file-source-sink/
jenkins/
kafka-connect-soap-source/
sync-api/
ctrl-jobscheduler/
file-vault-app/
kafka-connect-bigquery-sink/
kafka-connect-spreadsheets-sink/
admin-cache/
ctrl-listeners/
flink-http-client/
kafka-connect-bigquery-source/
kafka-probe-service/
azure-blob-probe-service/
ctrl-nodetaskmanager/
ftp-probe-service/
kafka-connect-documentdb-sink/
kafka-stream-connector/
probe-http/
test-container-app/
bigquery-probe-service/
gcs-probe-service/
kafka-connect-documentdb-source/
redis-probe-service/
transform-http/
box-service/
gdrive-probe-service/
kafka-connect-file-sink/
transform-kafka-stream/
cloud-job-connector/
kafka-connect-file-source/
license-generator/
transform-service/
delta-lake-service/
health-service/
kafka-connect-jdbc-sink/
license-service/
s3-probe-service/
transform-sql-service/
docker/
http-gateway/
kafka-connect-jdbc-source/
listing-app/
scheduled-k8s-jobs/
transform-sql-stream/
documentdb-service/
http-probe-service/
kafka-connect-kinesis-sink/
script-eval/
webdav-probe-service/
dropbox-probe-service/
http-sink/
kafka-connect-kinesis-source/
metrics-aggregation-stream/
script-runner/

.cache/

