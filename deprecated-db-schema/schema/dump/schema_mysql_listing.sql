CREATE DATABASE listing;
USE listing;
-- audit_connector_state definition

CREATE TABLE `audit_connector_state` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int NOT NULL,
  `state` varchar(20) NOT NULL,
  `message` text,
  `runtime` varchar(30),
  PRIMARY KEY (`id`, `audit_ts`)
) PARTITION BY RANGE ( UNIX_TIMESTAMP(audit_ts)) (
    PARTITION p2 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01 00:00:00')),
    PARTITION p3 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-15 00:00:00')),
    PARTITION p4 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01 00:00:00')),
    PARTITION p5 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-15 00:00:00')),
    PARTITION p6 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01 00:00:00')),
    PARTITION p7 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-15 00:00:00')),
    PARTITION p8 VALUES LESS THAN (UNIX_TIMESTAMP('2024-05-01 00:00:00')),
    PARTITION p9 VALUES LESS THAN (UNIX_TIMESTAMP('2024-05-15 00:00:00')),
    PARTITION p10 VALUES LESS THAN (UNIX_TIMESTAMP('2024-06-01 00:00:00')),
    PARTITION p11 VALUES LESS THAN (UNIX_TIMESTAMP('2024-06-15 00:00:00')),
    PARTITION p12 VALUES LESS THAN (UNIX_TIMESTAMP('2024-07-01 00:00:00')),
    PARTITION p13 VALUES LESS THAN (UNIX_TIMESTAMP('2024-07-15 00:00:00')),
    PARTITION p14 VALUES LESS THAN (UNIX_TIMESTAMP('2024-08-01 00:00:00')),
    PARTITION p15 VALUES LESS THAN (UNIX_TIMESTAMP('2024-08-15 00:00:00')),
    PARTITION p16 VALUES LESS THAN (UNIX_TIMESTAMP('2024-09-01 00:00:00')),
    PARTITION p17 VALUES LESS THAN (UNIX_TIMESTAMP('2024-09-15 00:00:00')),
    PARTITION p18 VALUES LESS THAN (UNIX_TIMESTAMP('2024-10-01 00:00:00')),
    PARTITION p19 VALUES LESS THAN (UNIX_TIMESTAMP('2024-10-15 00:00:00')),
    PARTITION p20 VALUES LESS THAN (UNIX_TIMESTAMP('2024-11-01 00:00:00')),
    PARTITION p21 VALUES LESS THAN (UNIX_TIMESTAMP('2024-11-15 00:00:00')),
    PARTITION p22 VALUES LESS THAN (UNIX_TIMESTAMP('2024-12-01 00:00:00')),
    PARTITION p23 VALUES LESS THAN (UNIX_TIMESTAMP('2024-12-15 00:00:00')),
    PARTITION p24 VALUES LESS THAN (UNIX_TIMESTAMP('2025-01-01 00:00:00')),
    PARTITION pN VALUES LESS THAN MAXVALUE
);


-- audit_fast_offset definition

CREATE TABLE `audit_fast_offset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `source_id` int(11) NOT NULL,
  `sink_id` int(11) NOT NULL,
  `offset_position` text,
  PRIMARY KEY (`id`)
);


-- audit_key_value definition

CREATE TABLE `audit_key_value` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `vendor_key` varchar(100) DEFAULT NULL,
  `vendor_value` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
);


-- audit_pipeline_node definition

CREATE TABLE `audit_pipeline_node` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `node_id` varchar(36) NOT NULL,
  `task_type` varchar(36) NOT NULL,
  `ip` varchar(36) DEFAULT NULL,
  `pod_name` varchar(256) DEFAULT NULL,
  `on_demand` tinyint(1) DEFAULT NULL,
  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);


-- audit_pipeline_task definition

CREATE TABLE `audit_pipeline_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `task_id` varchar(256) NOT NULL,
  `task_type` varchar(36) NOT NULL,
  `max_instances` int(11) DEFAULT NULL,
  `dedicated` tinyint(1) DEFAULT NULL,
  `state` varchar(36) NOT NULL,
  `meta` text,
  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
  `last_active_ts` timestamp(3) NULL DEFAULT NULL,
  `last_data_ts` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);


-- audit_pipeline_task_node definition

CREATE TABLE `audit_pipeline_task_node` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `node_id` varchar(36) NOT NULL,
  `task_id` varchar(36) NOT NULL,
  `state` varchar(36) NOT NULL,
  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
  `last_data_ts` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);


-- audit_sink_offset definition

CREATE TABLE `audit_sink_offset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `resource_id` int(11) NOT NULL,
  `offset_position` text,
  PRIMARY KEY (`id`)
);


-- audit_topic_offset definition

CREATE TABLE `audit_topic_offset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `dataset_id` int(11) NOT NULL,
  `partition_number` int(11) NOT NULL,
  `topic` varchar(300) NOT NULL,
  `topic_offset` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
);


-- connector_state definition

CREATE TABLE `connector_state` (
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `state` varchar(20) NOT NULL,
  `message` text,
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `runtime` varchar(30) DEFAULT 'UNSET',
  PRIMARY KEY (`resource_type`,`resource_id`)
);


-- dataset_statistics definition

CREATE TABLE `dataset_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dataset_id` int(11) DEFAULT NULL,
  `topic` varchar(256) DEFAULT NULL,
  `start_offsets` text,
  `end_offsets` text,
  `status` varchar(32) DEFAULT NULL,
  `submission_id` varchar(32) DEFAULT NULL,
  `statistics_json` text,
  `attempt` int(11) DEFAULT NULL,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `started_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `finished_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`)
);


-- fast_offset definition

CREATE TABLE `fast_offset` (
  `source_id` int(11) NOT NULL,
  `sink_id` int(11) NOT NULL,
  `offset_position` text,
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`source_id`,`sink_id`)
);


-- file_listing definition

CREATE TABLE `file_listing` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `connection_type` varchar(20) NOT NULL,
  `full_path` varchar(514) NOT NULL,
  `last_modified` bigint(20) DEFAULT NULL,
  `hash` varchar(100) DEFAULT NULL,
  `file_size` bigint(20) DEFAULT NULL,
  `link_to_original` bigint(20) DEFAULT NULL,
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `status` varchar(20) NOT NULL,
  `source` varchar(20) NOT NULL,
  `metadata` text,
  `last_message_offset` bigint(20) DEFAULT NULL,
  `message` varchar(300) DEFAULT NULL,
  `schema_detection_attempted` tinyint(1) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `attempts_count` int NOT NULL DEFAULT '0',
  `pod_name` varchar(70) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `file_listing_full_path_resouce_id` (`resource_id`,`full_path`),
  KEY `file_listing_status` (`status`),
  KEY `file_listing_resource_id_status` (`resource_id`,`status`)
);


-- key_value definition

CREATE TABLE `key_value` (
  `vendor_key` varchar(100) DEFAULT NULL,
  `vendor_value` varchar(100) DEFAULT NULL
);


-- pipeline_node definition

CREATE TABLE `pipeline_node` (
  `node_id` varchar(36) NOT NULL,
  `task_type` varchar(36) NOT NULL,
  `ip` varchar(36) DEFAULT NULL,
  `pod_name` varchar(256) DEFAULT NULL,
  `dedicated` tinyint(1) DEFAULT NULL,
  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
  `decomission` tinyint(1) DEFAULT NULL,
  `remove` tinyint(1) DEFAULT NULL,
  `tags` varchar(36) DEFAULT NULL,
  `version` varchar(16) DEFAULT NULL,
  `ctrl_transport` tinyint(1) DEFAULT NULL,
  `service_name` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`node_id`)
);


-- pipeline_run_state definition

CREATE TABLE `pipeline_run_state` (
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `run_id` bigint(20) NOT NULL,
  `status` varchar(32) DEFAULT NULL,
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `trace_message` tinyint(1) DEFAULT NULL,
  `runtime` varchar(30) DEFAULT 'UNSET',
  `done_on_timeout` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`resource_type`,`resource_id`,`run_id`)
);

CREATE INDEX pipeline_run_state_status_IDX USING BTREE ON pipeline_run_state (status);
CREATE INDEX pipeline_run_state_last_modified_IDX USING BTREE ON pipeline_run_state (last_modified);


-- pipeline_task definition

CREATE TABLE `pipeline_task` (
  `task_id` varchar(256) NOT NULL,
  `task_type` varchar(36) NOT NULL,
  `max_instances` int(11) DEFAULT NULL,
  `dedicated` tinyint(1) DEFAULT NULL,
  `state` varchar(36) NOT NULL,
  `meta` text,
  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
  `last_active_ts` timestamp(3) NULL DEFAULT NULL,
  `last_data_ts` timestamp(3) NULL DEFAULT NULL,
  `excluded` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`task_id`)
);


-- pipeline_task_node definition

CREATE TABLE `pipeline_task_node` (
  `node_id` varchar(36) NOT NULL,
  `task_id` varchar(36) NOT NULL,
  `state` varchar(36) NOT NULL,
  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
  `last_data_ts` timestamp(3) NULL DEFAULT NULL,
  `run_id` bigint(20) DEFAULT NULL,
  `write_state` varchar(36) DEFAULT NULL,
  `read_state` varchar(36) DEFAULT NULL,
  `read_start_ts` timestamp(3) NULL DEFAULT NULL,
  `read_done_ts` timestamp(3) NULL DEFAULT NULL,
  `write_start_ts` timestamp(3) NULL DEFAULT NULL,
  `write_done_ts` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`node_id`,`task_id`)
);


-- pipeline_task_node_phantoms definition

CREATE TABLE `pipeline_task_node_phantoms` (
  `node_id` varchar(36) NOT NULL,
  `task_id` varchar(36) NOT NULL,
  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
  `last_data_ts` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`node_id`,`task_id`)
);


-- pipeline_task_run definition

CREATE TABLE `pipeline_task_run` (
  `task_id` varchar(256) NOT NULL,
  `task_type` varchar(36) NOT NULL,
  `run_id` bigint(20) NOT NULL,
  `read_state` varchar(36) NOT NULL,
  `read_start_ts` timestamp(3) NULL DEFAULT NULL,
  `read_done_ts` timestamp(3) NULL DEFAULT NULL,
  `write_state` varchar(36) NOT NULL,
  `write_start_ts` timestamp(3) NULL DEFAULT NULL,
  `write_done_ts` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`task_id`,`run_id`)
);


-- quarantine_topic_offset definition

CREATE TABLE `quarantine_topic_offset` (
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `dataset_id` int(11) DEFAULT NULL,
  `partition_number` int(11) NOT NULL,
  `topic` varchar(300) NOT NULL,
  `offset` bigint(20) DEFAULT NULL,
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`resource_type`,`resource_id`,`topic`,`partition_number`)
);


-- script_log definition

CREATE TABLE `script_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `resource_type` varchar(10) NOT NULL,
  `connection_type` varchar(20) NOT NULL,
  `start_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `end_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `status` varchar(20) NOT NULL,
  `state` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_script_log_resource_id_start_time` (`resource_id`,`start_time`)
);


-- sink_offset definition

CREATE TABLE `sink_offset` (
  `resource_id` int(11) NOT NULL,
  `offset_position` text,
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`resource_id`)
);


-- topic_offset definition

CREATE TABLE `topic_offset` (
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `dataset_id` int(11) NOT NULL,
  `partition_number` int(11) NOT NULL,
  `topic` varchar(300) NOT NULL,
  `topic_offset` bigint(20) DEFAULT NULL,
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`resource_type`,`resource_id`,`dataset_id`,`topic`,`partition_number`)
);


-- dataset_trace definition

CREATE TABLE `dataset_trace` (
  `resource_id` int(11) NOT NULL,
  `run_id` bigint(20) NOT NULL,
  `traces` int(11) NOT NULL,
  `tx_state` varchar(36) NOT NULL,
  `tx_done_ts` TIMESTAMP(3) NULL,
  `updated_at` TIMESTAMP(3) NULL,
  `created_at` TIMESTAMP(3) NULL,
  PRIMARY KEY (`resource_id`, `run_id`)
);

-- flink_job definition

CREATE TABLE `flink_job` (
                           `job_id` varchar(100) NOT NULL,
                           `resource_id` int(11) NOT NULL,
                           `job_state` varchar(20) NOT NULL,
                           `service_log` text DEFAULT NULL,
                           `savepoint` text DEFAULT NULL,
                           `records_sent` bigint NOT NULL DEFAULT 0,
                           `bytes_sent` bigint NOT NULL DEFAULT 0,
                           `error_count` bigint NOT NULL DEFAULT 0,
                           `transform_data` text,
                           `deleted` tinyint(1) NOT NULL DEFAULT '0',
                           `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
                           `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
                           PRIMARY KEY (`job_id`)
);


DROP TRIGGER IF EXISTS connector_state_update;

DELIMITER $$
$$
CREATE DEFINER=`admin`@`%` TRIGGER connector_state_update
  AFTER UPDATE ON connector_state FOR EACH ROW
  INSERT INTO audit_connector_state(`action`, `audit_ts`, `resource_type`, `resource_id`, `state`, `message`, `runtime`)
        VALUES ('update', current_timestamp, new.resource_type, new.resource_id, new.state, new.message, new.runtime)$$
DELIMITER ;


DROP TRIGGER IF EXISTS connector_state_insert;

DELIMITER $$
$$
CREATE DEFINER=`admin`@`%` TRIGGER connector_state_insert
  AFTER INSERT ON connector_state FOR EACH ROW
  INSERT INTO audit_connector_state(`action`, `audit_ts`, `resource_type`, `resource_id`, `state`, `message`, `runtime`)
        VALUES ('insert', current_timestamp, new.resource_type, new.resource_id, new.state, new.message, new.runtime)$$
DELIMITER ;


DROP TRIGGER IF EXISTS connector_state_delete;

DELIMITER $$
$$
CREATE DEFINER=`admin`@`%` TRIGGER connector_state_delete
  AFTER DELETE ON connector_state FOR EACH ROW
  INSERT INTO audit_connector_state(`action`, `audit_ts`, `resource_type`, `resource_id`, `state`, `message`, `runtime`)
        VALUES ('delete', current_timestamp, old.resource_type, old.resource_id, old.state, old.message, old.runtime)$$
DELIMITER ;


CREATE TRIGGER sink_offset_insert
  AFTER INSERT ON sink_offset FOR EACH ROW
  INSERT INTO audit_sink_offset(`action`, `audit_ts`, `resource_id`, `offset_position`)
        VALUES ('insert', current_timestamp(3), new.resource_id, new.offset_position);

CREATE TRIGGER sink_offset_update
  AFTER UPDATE ON sink_offset FOR EACH ROW
  INSERT INTO audit_sink_offset(`action`, `audit_ts`, `resource_id`, `offset_position`)
        VALUES ('update', current_timestamp(3), new.resource_id, new.offset_position);

CREATE TRIGGER sink_offset_delete
  AFTER DELETE ON sink_offset FOR EACH ROW
  INSERT INTO audit_sink_offset(`action`, `audit_ts`, `resource_id`, `offset_position`)
        VALUES ('delete', current_timestamp(3), old.resource_id, old.offset_position);

INSERT INTO key_value (vendor_key, vendor_value) VALUES('oauth2_vendors', '[]');

CREATE TABLE `semaphore` (
  `name` VARCHAR(255) NOT NULL,
  `type` VARCHAR(36) NOT NULL,
  `status` VARCHAR(36) NOT NULL,
  `acquired_at` TIMESTAMP(3) NOT NULL,
  `expires_at` TIMESTAMP(3) NOT NULL,
  `finished_at` TIMESTAMP(3) DEFAULT NULL,
  `success_at` TIMESTAMP(3) DEFAULT NULL,
  `resource_type` varchar(10),
  `resource_id` int(11),
  `pod_name` VARCHAR(70),
  `message` TEXT DEFAULT NULL,
  PRIMARY KEY (`name`)
);

CREATE TABLE `license` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `license_key` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `canary_rules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `application` VARCHAR(255) NOT NULL,
  `percentage` int(11),
  `org_id` int(11),
  `flow_id` int(11),
  `canary_value` VARCHAR(255),
  `rule_json` text,
  `start_time` timestamp(3) NULL DEFAULT NULL,
  `end_time` timestamp(3) NULL DEFAULT NULL,
  `disabled` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`)
);

CREATE TABLE `cloud_environment` (
    `source_id` integer NOT NULL,
    `sink_id` integer NOT NULL,
    `cluster_id` varchar(256) NOT NULL,
    `last_operation_ts` timestamp(3) NULL DEFAULT NULL,
    `last_status` varchar(256) DEFAULT 'NEW',
    PRIMARY KEY (`source_id`,`sink_id`,`cluster_id`,`last_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `connector_sync`(
    `type`          VARCHAR(32) NOT NULL,
    `connector_id`  VARCHAR(256) NOT NULL,
    `task_id`       VARCHAR(256) NOT NULL,
    `phase`         VARCHAR(32)  NOT NULL,
    `service_name`  VARCHAR(70) NOT NULL,
    `pod_name`      VARCHAR(70)  NOT NULL,
    `context`       TEXT DEFAULT NULL,
    `last_modified` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`type`, `connector_id`, `task_id`)
);

CREATE TABLE `audit_control` (
	`id`            BIGINT auto_increment NOT NULL,
	`message_id`    VARCHAR(100) DEFAULT NULL,
	`resource_type` VARCHAR(30) NOT NULL,
	`event_type`    VARCHAR(30) NOT NULL,
	`resource_id`   INT NOT NULL,
	`origin`        VARCHAR(100) NOT NULL,
	`created_at`    TIMESTAMP NOT NULL,
	`body`          TEXT NOT NULL,
	`audit_ts`      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id, audit_ts)
) PARTITION BY RANGE ( UNIX_TIMESTAMP(audit_ts)) (
      PARTITION p2401 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01 00:00:00')),
      PARTITION p2402 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01 00:00:00')),
      PARTITION p2403 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01 00:00:00')),
      PARTITION p2404 VALUES LESS THAN (UNIX_TIMESTAMP('2024-05-01 00:00:00')),
      PARTITION p2405 VALUES LESS THAN (UNIX_TIMESTAMP('2024-06-01 00:00:00')),
      PARTITION p2406 VALUES LESS THAN (UNIX_TIMESTAMP('2024-07-01 00:00:00')),
      PARTITION p2407 VALUES LESS THAN (UNIX_TIMESTAMP('2024-08-01 00:00:00')),
      PARTITION p2408 VALUES LESS THAN (UNIX_TIMESTAMP('2024-09-01 00:00:00')),
      PARTITION p2409 VALUES LESS THAN (UNIX_TIMESTAMP('2024-10-01 00:00:00')),
      PARTITION p2410 VALUES LESS THAN (UNIX_TIMESTAMP('2024-11-01 00:00:00')),
      PARTITION p2411 VALUES LESS THAN (UNIX_TIMESTAMP('2024-12-01 00:00:00')),
      PARTITION p2412 VALUES LESS THAN (UNIX_TIMESTAMP('2025-01-01 00:00:00')),
      PARTITION pN VALUES LESS THAN MAXVALUE
);

CREATE TABLE `audit_coordination` (
	`id`         BIGINT auto_increment NOT NULL,
	`message_id` VARCHAR(100) DEFAULT NULL,
	`event_type` VARCHAR(30) NOT NULL,
	`created_at` TIMESTAMP NOT NULL,
	`body`       TEXT NOT NULL,
	`audit_ts`   TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id, audit_ts)
) PARTITION BY RANGE ( UNIX_TIMESTAMP(audit_ts)) (
       PARTITION p2401 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01 00:00:00')),
       PARTITION p2402 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01 00:00:00')),
       PARTITION p2403 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01 00:00:00')),
       PARTITION p2404 VALUES LESS THAN (UNIX_TIMESTAMP('2024-05-01 00:00:00')),
       PARTITION p2405 VALUES LESS THAN (UNIX_TIMESTAMP('2024-06-01 00:00:00')),
       PARTITION p2406 VALUES LESS THAN (UNIX_TIMESTAMP('2024-07-01 00:00:00')),
       PARTITION p2407 VALUES LESS THAN (UNIX_TIMESTAMP('2024-08-01 00:00:00')),
       PARTITION p2408 VALUES LESS THAN (UNIX_TIMESTAMP('2024-09-01 00:00:00')),
       PARTITION p2409 VALUES LESS THAN (UNIX_TIMESTAMP('2024-10-01 00:00:00')),
       PARTITION p2410 VALUES LESS THAN (UNIX_TIMESTAMP('2024-11-01 00:00:00')),
       PARTITION p2411 VALUES LESS THAN (UNIX_TIMESTAMP('2024-12-01 00:00:00')),
       PARTITION p2412 VALUES LESS THAN (UNIX_TIMESTAMP('2025-01-01 00:00:00')),
       PARTITION pN VALUES LESS THAN MAXVALUE
);

CREATE TABLE `audit_control_jobscheduler` (
	`id`            BIGINT auto_increment NOT NULL,
	`message_id`    VARCHAR(100) DEFAULT NULL,
	`resource_type` VARCHAR(30) NOT NULL,
	`event_type`    VARCHAR(30) NOT NULL,
	`resource_id`   INT NOT NULL,
	`origin`        VARCHAR(100) NOT NULL,
	`created_at`    TIMESTAMP NOT NULL,
	`body`          TEXT NOT NULL,
	`audit_ts`      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id, audit_ts)
) PARTITION BY RANGE ( UNIX_TIMESTAMP(audit_ts)) (
       PARTITION p2401 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01 00:00:00')),
       PARTITION p2402 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01 00:00:00')),
       PARTITION p2403 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01 00:00:00')),
       PARTITION p2404 VALUES LESS THAN (UNIX_TIMESTAMP('2024-05-01 00:00:00')),
       PARTITION p2405 VALUES LESS THAN (UNIX_TIMESTAMP('2024-06-01 00:00:00')),
       PARTITION p2406 VALUES LESS THAN (UNIX_TIMESTAMP('2024-07-01 00:00:00')),
       PARTITION p2407 VALUES LESS THAN (UNIX_TIMESTAMP('2024-08-01 00:00:00')),
       PARTITION p2408 VALUES LESS THAN (UNIX_TIMESTAMP('2024-09-01 00:00:00')),
       PARTITION p2409 VALUES LESS THAN (UNIX_TIMESTAMP('2024-10-01 00:00:00')),
       PARTITION p2410 VALUES LESS THAN (UNIX_TIMESTAMP('2024-11-01 00:00:00')),
       PARTITION p2411 VALUES LESS THAN (UNIX_TIMESTAMP('2024-12-01 00:00:00')),
       PARTITION p2412 VALUES LESS THAN (UNIX_TIMESTAMP('2025-01-01 00:00:00')),
       PARTITION pN VALUES LESS THAN MAXVALUE
);

CREATE TABLE `runtime_status` (
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `status` varchar(32) NOT NULL,
  `last_modified_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `processing_until_ts` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`resource_type`,`resource_id`)
);

CREATE TABLE `pod_pool` (
  `pooled_service_name` varchar(128) NOT NULL,
  `docker_image` varchar(128) NOT NULL,
  `nexla_service_name` varchar(128),
  `status` varchar(32) NOT NULL DEFAULT 'PHANTOM',
  `last_modified_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `enabled` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`pooled_service_name`)
);