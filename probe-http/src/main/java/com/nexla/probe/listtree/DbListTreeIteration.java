package com.nexla.probe.listtree;

import com.nexla.common.probe.ColumnInfo;
import com.nexla.connector.config.jdbc.DbListTreeService;
import lombok.Data;
import one.util.streamex.StreamEx;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;

import static com.nexla.common.probe.ProbeControllerConstants.TYPE;
import static com.nexla.common.probe.ProbeControllerConstants.VALUE;

@Data
public class DbListTreeIteration<T> {

	public static final String DEFAULT_DATABASE = "Database";
	private final T config;
	private final DbListTreeService<T> probeService;

	public TreeMap<String, Object> listTree(Optional<String> database, Optional<String> schema, Optional<String> table) {
		if (database.isEmpty()) {
			return listTreeDatabase(config);
		} else if (schema.isEmpty() && table.isEmpty()) {
			return listTreeSchema(config, database);
		} else if (table.isEmpty()) {
			return listTreeTables(config, database, schema);
		} else {
			return listTreeColumns(config, database, schema, table);
		}
	}

	private TreeMap<String, Object> listTreeDatabase(T config) {
		List<String> listDatabases = probeService.listDatabases(config).toList();

		if (listDatabases.size() > 1) {
			TreeMap<String, Object> resultMap = new TreeMap<>();
			listDatabases
				.forEach(dsName -> {
					TreeMap<String, Object> value = new TreeMap<>();
					value.put(TYPE, "database");
					value.put(VALUE, new TreeMap<>());
					resultMap.put(dsName, value);
				});
			return resultMap;
		} else {
			String databaseName = listDatabases.stream()
					.filter(Objects::nonNull)
					.findFirst()
					.orElse(DEFAULT_DATABASE);
			return listTreeSchema(config, Optional.of(databaseName));
		}
	}

	private TreeMap<String, Object> listTreeSchema(T config, Optional<String> database) {
		List<String> listSchemas = probeService.listSchemas(database.get(), config).toList();
		if (listSchemas.isEmpty() || listSchemas.size() == 1) {
			return listTreeTables(config, database, Optional.empty());
		} else {
			TreeMap<String, Object> resultMap = new TreeMap<>();
			TreeMap<String, Object> databaseMap = new TreeMap<>();
			TreeMap<String, Object> schemaMap = new TreeMap<>();
			databaseMap.put(TYPE, "database");
			databaseMap.put(VALUE, schemaMap);
			listSchemas
				.forEach(t -> {
					TreeMap<Object, Object> val2 = new TreeMap<>();
					val2.put(TYPE, "schema");
					val2.put(VALUE, new TreeMap<>());
					schemaMap.put(t, val2);
				});
			resultMap.put(database.get(), databaseMap);
			return resultMap;
		}
	}

	private TreeMap<String, Object> listTreeTables(T config, Optional<String> database, Optional<String> schema) {
		TreeMap<String, Object> resultMap = new TreeMap<>();

		TreeMap<String, Object> dsMap = new TreeMap<>();
		TreeMap<String, Object> tablesMap = new TreeMap<>();
		dsMap.put(TYPE, "database");
		dsMap.put(VALUE, tablesMap);
		
		// List tables
		probeService.listTables(database.get(), schema, config)
			.forEach(t -> {
				TreeMap<Object, Object> val2 = new TreeMap<>();
				val2.put(TYPE, "table");
				val2.put(VALUE, new TreeMap<>());
				tablesMap.put(t, val2);
			});

		// List volumes for Databricks Unity Catalog (if supported)
		if (database.isPresent() && schema.isPresent()) {
			try {
				probeService.listVolumes(database.get(), schema.get(), config)
					.forEach(v -> {
						TreeMap<Object, Object> val2 = new TreeMap<>();
						val2.put(TYPE, "volume");
						val2.put(VALUE, new TreeMap<>());
						tablesMap.put(v, val2);
					});
			} catch (Exception e) {
				// Ignore volume listing errors - not all databases support volumes
			}
		}

		resultMap.put(database.get(), dsMap);
		return resultMap;
	}

	private TreeMap<String, Object> listTreeColumns(T config, Optional<String> database, Optional<String> schema, Optional<String> table) {
		TreeMap<String, Object> resultMap = new TreeMap<>();

		TreeMap<String, Object> dsMap = new TreeMap<>();
		TreeMap<String, Object> tablesMap = new TreeMap<>();
		TreeMap<String, Object> tableMap = new TreeMap<>();
		dsMap.put(TYPE, "database");
		dsMap.put(VALUE, tablesMap);
		tableMap.put(TYPE, "table");
		List<ColumnInfo> columns = probeService.listColumnInfos(database.get(), schema, table.get(), config);
		List<ColumnInfo> columnsSorted = StreamEx.of(columns)
			.sorted(Comparator.comparing(o -> o.name))
			.toList();
		tableMap.put(VALUE, columnsSorted);
		tablesMap.put(table.get(), tableMap);
		resultMap.put(database.get(), dsMap);
		return resultMap;
	}



}
