package com.nexla.common.transform;

import com.bazaarvoice.jolt.JsonUtils;
import java.util.*;

public class ObjectWalker {
  private final boolean unwrapJson;

  public ObjectWalker() {
    this(false);
  }

  public ObjectWalker(boolean unwrapJson) {
    this.unwrapJson = unwrapJson;
  }

  public interface Callback {
    void onObject(Deque<String> path, Object o);
  }

  public void walk(Object input, Callback callback) {
    object(new ArrayDeque<>(), input, callback, false);
  }

  public void expandWalk(Object input, Callback callback) {
    object(new ArrayDeque<>(), input, callback, true);
  }

  @SuppressWarnings("unchecked")
  private void object(
      Deque<String> pathStack, Object input, Callback callback, boolean callbackAllLevels) {
    if (input instanceof Map) {
      flattenMap(pathStack, (Map<String, Object>) input, callback, callbackAllLevels);
    } else if (input instanceof List) {
      flattenList(pathStack, (List<Object>) input, callback, callbackAllLevels);
    } else if (input instanceof Optional) {
      ((Optional<Object>) input).ifPresent(o -> object(pathStack, o, callback, callbackAllLevels));
    } else {
      if (unwrapJson && input instanceof String) {
        try {
          object(pathStack, JsonUtils.jsonToObject((String) input), callback, callbackAllLevels);
          return;
        } catch (Exception e) {
          // ignore
        }
      }

      // with callbackAllLevels each key is part of an array or object iteration, so it was already
      // called
      if (!callbackAllLevels) {
        callback.onObject(new ArrayDeque<>(pathStack), input);
      }
    }
  }

  private void flattenMap(
      Deque<String> pathStack,
      Map<String, Object> input,
      Callback callback,
      boolean callbackAllLevels) {
    for (String key : input.keySet()) {
      pathStack.push(key);
      if (callbackAllLevels) {
        callback.onObject(new ArrayDeque<>(pathStack), input.get(key));
      }
      object(pathStack, input.get(key), callback, callbackAllLevels);
      pathStack.pop();
    }
  }

  private void flattenList(
      Deque<String> pathStack, List<Object> input, Callback callback, boolean callbackAllLevels) {
    for (int i = 0; i < input.size(); i++) {
      pathStack.push("" + i);
      if (callbackAllLevels) {
        callback.onObject(new ArrayDeque<>(pathStack), input.get(i));
      }
      object(pathStack, input.get(i), callback, callbackAllLevels);
      pathStack.pop();
    }
  }
}
