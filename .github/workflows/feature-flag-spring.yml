name: feature-flag-spring

on:
  push:
    branches: [ main ]
    paths:
      - feature-flag-spring/**
      - .github/workflows/feature-flag-spring.yml
  pull_request:
    branches: [ main ]
    paths:
      - feature-flag-spring/**
      - .github/workflows/feature-flag-spring.yml

jobs:
  build:
    name: Build Library
    permissions:
      contents: read
      pull-requests: write
    uses: nexla/cloud-actions/.github/workflows/reusable-gradle-library-build-deploy.yml@v1
    with:
      library: feature-flag-spring
      library-root: feature-flag-spring
      java-version: 17
      publish: false
